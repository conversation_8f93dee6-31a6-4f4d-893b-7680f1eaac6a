import Stripe from 'stripe';

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-02-24.acacia',
});

export const STRIPE_CONFIG = {
  PRICE_IDS: {
    FREE: process.env.STRIPE_FREE_PRICE_ID!,
    STARTER: process.env.STRIPE_STARTER_PRICE_ID!,
    PROFESSIONAL: process.env.STRIPE_PROFESSIONAL_PRICE_ID!,
    ENTERPRISE: process.env.STRIPE_ENTERPRISE_PRICE_ID!,
  },
  PRODUCT_IDS: {
    FREE: process.env.STRIPE_FREE_PRODUCT_ID!,
    STARTER: process.env.STRIPE_STARTER_PRODUCT_ID!,
    PROFESSIONAL: process.env.STRIPE_PROFESSIONAL_PRODUCT_ID!,
    ENTERPRISE: process.env.STRIPE_ENTERPRISE_PRODUCT_ID!,
  },
};

export type SubscriptionTier = 'free' | 'starter' | 'professional' | 'enterprise';

export interface TierConfig {
  name: string;
  price: string;
  priceId: string | null; // null for free tier
  productId: string | null; // null for free tier
  features: string[];
  limits: {
    configurations: number;
    apiKeysPerConfig: number;
    apiRequests: number;
    workflows: number;
    workflowExecutions: number;
    canUseAiNodes: boolean;
    canUseKnowledgeBase: boolean;
    knowledgeBaseDocuments: number;
    teamMembers: number;
  };
}

export const TIER_CONFIGS: Record<SubscriptionTier, TierConfig> = {
  free: {
    name: 'Free',
    price: '$0',
    priceId: STRIPE_CONFIG.PRICE_IDS.FREE,
    productId: STRIPE_CONFIG.PRODUCT_IDS.FREE,
    features: [
      'Unlimited API requests',
      '1 Custom Configuration',
      '3 API Keys per config',
      'All 300+ AI models',
      'Strict fallback routing only',
      'Basic analytics (7-day history)',
      'Community support',
      '3 Active workflows',
      '100 Workflow executions/month',
    ],
    limits: {
      configurations: 1,
      apiKeysPerConfig: 3,
      apiRequests: 999999,
      workflows: 3,
      workflowExecutions: 100,
      canUseAiNodes: false,
      canUseKnowledgeBase: false,
      knowledgeBaseDocuments: 0,
      teamMembers: 1,
    },
  },
  starter: {
    name: 'Starter',
    price: '$19',
    priceId: STRIPE_CONFIG.PRICE_IDS.STARTER,
    productId: STRIPE_CONFIG.PRODUCT_IDS.STARTER,
    features: [
      'Unlimited API requests',
      '4 Custom Configurations',
      '5 API Keys per config',
      'All 300+ AI models',
      'Advanced routing strategies',
      'Up to 3 custom roles',
      'Intelligent role routing (1 config)',
      'Prompt engineering (no file upload)',
      'Enhanced analytics (30-day history)',
      '10 Active workflows',
      '1,000 Workflow executions/month',
      'AI workflow nodes',
    ],
    limits: {
      configurations: 4,
      apiKeysPerConfig: 5,
      apiRequests: 999999,
      workflows: 10,
      workflowExecutions: 1000,
      canUseAiNodes: true,
      canUseKnowledgeBase: false,
      knowledgeBaseDocuments: 0,
      teamMembers: 1,
    },
  },
  professional: {
    name: 'Professional',
    price: '$49',
    priceId: STRIPE_CONFIG.PRICE_IDS.PROFESSIONAL,
    productId: STRIPE_CONFIG.PRODUCT_IDS.PROFESSIONAL,
    features: [
      'Unlimited API requests',
      '20 Custom Configurations',
      '15 API Keys per config',
      'All 300+ AI models',
      'All advanced routing strategies',
      'Unlimited custom roles',
      'Prompt engineering + Knowledge base',
      '5 Documents per knowledge base',
      'Semantic caching',
      'Advanced analytics (90-day history)',
      '50 Active workflows',
      '10,000 Workflow executions/month',
      'Team collaboration (up to 5 members)',
      'Priority email support',
    ],
    limits: {
      configurations: 20,
      apiKeysPerConfig: 15,
      apiRequests: 999999,
      workflows: 50,
      workflowExecutions: 10000,
      canUseAiNodes: true,
      canUseKnowledgeBase: true,
      knowledgeBaseDocuments: 5,
      teamMembers: 5,
    },
  },
  enterprise: {
    name: 'Enterprise',
    price: '$149',
    priceId: STRIPE_CONFIG.PRICE_IDS.ENTERPRISE,
    productId: STRIPE_CONFIG.PRODUCT_IDS.ENTERPRISE,
    features: [
      'Unlimited API requests',
      'Unlimited configurations',
      'Unlimited API keys',
      'All 300+ models + priority access',
      'All routing strategies',
      'Unlimited custom roles',
      'Advanced prompt engineering',
      'Unlimited knowledge base documents',
      'Advanced semantic caching',
      'Enterprise analytics (1-year history)',
      'Unlimited workflows',
      'Unlimited workflow executions',
      'Unlimited team members',
      'Custom integrations',
      'Dedicated support + phone',
      'SLA guarantee',
    ],
    limits: {
      configurations: 999999,
      apiKeysPerConfig: 999999,
      apiRequests: 999999,
      workflows: 999999,
      workflowExecutions: 999999,
      canUseAiNodes: true,
      canUseKnowledgeBase: true,
      knowledgeBaseDocuments: 999999,
      teamMembers: 999999,
    },
  },
};

export function getTierConfig(tier: SubscriptionTier): TierConfig {
  return TIER_CONFIGS[tier];
}

export function getPriceIdForTier(tier: SubscriptionTier): string | null {
  return TIER_CONFIGS[tier].priceId;
}

export function getTierFromPriceId(priceId: string): SubscriptionTier {
  for (const [tier, config] of Object.entries(TIER_CONFIGS)) {
    if (config.priceId === priceId) {
      return tier as SubscriptionTier;
    }
  }
  return 'free'; // Default fallback to free tier
}

export function formatPrice(tier: SubscriptionTier): string {
  return TIER_CONFIGS[tier].price;
}

export function canPerformAction(
  tier: SubscriptionTier,
  action: 'create_config' | 'create_api_key' | 'create_workflow' | 'execute_workflow',
  currentCount: number,
  monthlyCount?: number
): boolean {
  const limits = TIER_CONFIGS[tier].limits;

  switch (action) {
    case 'create_config':
      return currentCount < limits.configurations;
    case 'create_api_key':
      return currentCount < limits.apiKeysPerConfig;
    case 'create_workflow':
      return currentCount < limits.workflows;
    case 'execute_workflow':
      return monthlyCount ? monthlyCount < limits.workflowExecutions : true;
    default:
      return true;
  }
}

export function hasFeatureAccess(
  tier: SubscriptionTier,
  feature: 'ai_nodes' | 'knowledge_base' | 'team_collaboration' | 'advanced_routing'
): boolean {
  const limits = TIER_CONFIGS[tier].limits;

  switch (feature) {
    case 'ai_nodes':
      return limits.canUseAiNodes;
    case 'knowledge_base':
      return limits.canUseKnowledgeBase;
    case 'team_collaboration':
      return limits.teamMembers > 1;
    case 'advanced_routing':
      return tier !== 'free'; // All paid tiers have advanced routing
    default:
      return false;
  }
}

export interface SubscriptionStatus {
  hasActiveSubscription: boolean;
  tier: SubscriptionTier;
  status: string | null;
  currentPeriodEnd: string | null;
  cancelAtPeriodEnd: boolean;
  isFree: boolean;
}

export interface UsageStatus {
  tier: SubscriptionTier;
  usage: {
    configurations: number;
    apiKeys: number;
    apiRequests: number;
    workflows: number;
    workflowExecutions: number;
  };
  limits: {
    configurations: number;
    apiKeysPerConfig: number;
    apiRequests: number;
    workflows: number;
    workflowExecutions: number;
    canUseAiNodes: boolean;
    canUseKnowledgeBase: boolean;
    knowledgeBaseDocuments: number;
    teamMembers: number;
  };
  canCreateConfig: boolean;
  canCreateApiKey: boolean;
  canCreateWorkflow: boolean;
  canExecuteWorkflow: boolean;
}

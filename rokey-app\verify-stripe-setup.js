// Stripe Setup Verification Script
// Run this to verify your Stripe price IDs are correct

// Load environment variables first
require('dotenv').config({ path: '.env.local' });

// Test environment detection
const isProduction = process.env.NODE_ENV === 'production';
console.log('🌍 Environment:', isProduction ? 'PRODUCTION' : 'DEVELOPMENT');

// Select appropriate keys
const secretKey = isProduction
  ? process.env.STRIPE_LIVE_SECRET_KEY
  : process.env.STRIPE_TEST_SECRET_KEY;

console.log('🔑 Using secret key:', secretKey?.substring(0, 20) + '...');

const stripe = require('stripe')(secretKey);

async function verifyStripeSetup() {
  console.log('🔍 Verifying Stripe Setup...\n');

  try {
    // Test Stripe connection
    console.log('✅ Stripe connection successful');
    
    // Get all products
    console.log('\n📦 Fetching all products...');
    const products = await stripe.products.list({ limit: 10 });
    
    console.log(`Found ${products.data.length} products:\n`);
    
    for (const product of products.data) {
      console.log(`Product: ${product.name} (${product.id})`);
      
      // Get prices for this product
      const prices = await stripe.prices.list({ 
        product: product.id,
        limit: 10 
      });
      
      for (const price of prices.data) {
        const amount = price.unit_amount ? `$${price.unit_amount / 100}` : 'Free';
        console.log(`  └─ Price: ${amount}/${price.recurring?.interval || 'one-time'} (${price.id})`);
      }
      console.log('');
    }

    // Test specific price IDs from environment
    console.log('🧪 Testing environment price IDs...\n');

    const priceIds = {
      'FREE': isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,
      'STARTER': isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,
      'PROFESSIONAL': isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,
      'ENTERPRISE': isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID,
    };

    for (const [tier, priceId] of Object.entries(priceIds)) {
      try {
        const price = await stripe.prices.retrieve(priceId);
        const amount = price.unit_amount ? `$${price.unit_amount / 100}` : 'Free';
        console.log(`✅ ${tier}: ${priceId} - ${amount}/${price.recurring?.interval || 'one-time'}`);
      } catch (error) {
        console.log(`❌ ${tier}: ${priceId} - ERROR: ${error.message}`);
      }
    }

  } catch (error) {
    console.error('❌ Stripe connection failed:', error.message);
  }
}

verifyStripeSetup();

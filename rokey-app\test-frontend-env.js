// Test Frontend Environment Variables
require('dotenv').config({ path: '.env.local' });

console.log('🧪 Testing Frontend Environment Variables...\n');

const isProduction = process.env.NODE_ENV === 'production';
console.log('🌍 Environment:', isProduction ? 'PRODUCTION' : 'DEVELOPMENT');

console.log('\n📋 Public Environment Variables:');
console.log('NEXT_PUBLIC_STRIPE_FREE_PRICE_ID:', process.env.NEXT_PUBLIC_STRIPE_FREE_PRICE_ID);
console.log('NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID:', process.env.NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID);
console.log('NEXT_PUBLIC_STRIPE_PROFESSIONAL_PRICE_ID:', process.env.NEXT_PUBLIC_STRIPE_PROFESSIONAL_PRICE_ID);
console.log('NEXT_PUBLIC_STRIPE_ENTERPRISE_PRICE_ID:', process.env.NEXT_PUBLIC_STRIPE_ENTERPRISE_PRICE_ID);

console.log('\n✅ Frontend should now use these price IDs for checkout!');
console.log('\n🎯 Test the Starter tier signup - it should work with price:', process.env.NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID);

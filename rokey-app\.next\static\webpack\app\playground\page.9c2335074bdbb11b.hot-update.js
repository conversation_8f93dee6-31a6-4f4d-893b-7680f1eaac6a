"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/utils/streamingUtils.ts":
/*!*************************************!*\
  !*** ./src/utils/streamingUtils.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERFORMANCE_THRESHOLDS: () => (/* binding */ PERFORMANCE_THRESHOLDS),\n/* harmony export */   createFirstTokenTrackingStream: () => (/* binding */ createFirstTokenTrackingStream),\n/* harmony export */   estimateTokenCount: () => (/* binding */ estimateTokenCount),\n/* harmony export */   evaluatePerformance: () => (/* binding */ evaluatePerformance),\n/* harmony export */   getProviderModelFromContext: () => (/* binding */ getProviderModelFromContext),\n/* harmony export */   logStreamingPerformance: () => (/* binding */ logStreamingPerformance)\n/* harmony export */ });\n// Streaming utilities for first token tracking and performance monitoring\nfunction createFirstTokenTrackingStream(originalStream, provider, model) {\n    const reader = originalStream.getReader();\n    const decoder = new TextDecoder();\n    const encoder = new TextEncoder();\n    return new ReadableStream({\n        async start (controller) {\n            let firstTokenSent = false;\n            const streamStartTime = Date.now();\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        controller.close();\n                        break;\n                    }\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    // Check if this chunk contains actual content (first token)\n                    if (!firstTokenSent && chunk.includes('delta')) {\n                        try {\n                            // Parse SSE data to check for content\n                            const lines = chunk.split('\\n');\n                            for (const line of lines){\n                                if (line.startsWith('data: ') && !line.includes('[DONE]')) {\n                                    const jsonData = line.substring(6);\n                                    try {\n                                        var _parsed_choices__delta, _parsed_choices_, _parsed_choices;\n                                        const parsed = JSON.parse(jsonData);\n                                        if ((_parsed_choices = parsed.choices) === null || _parsed_choices === void 0 ? void 0 : (_parsed_choices_ = _parsed_choices[0]) === null || _parsed_choices_ === void 0 ? void 0 : (_parsed_choices__delta = _parsed_choices_.delta) === null || _parsed_choices__delta === void 0 ? void 0 : _parsed_choices__delta.content) {\n                                            const firstTokenTime = Date.now() - streamStartTime;\n                                            console.log(\"\\uD83D\\uDE80 \".concat(provider.toUpperCase(), \" FIRST TOKEN: \").concat(firstTokenTime, \"ms (\").concat(model, \")\"));\n                                            firstTokenSent = true;\n                                            break;\n                                        }\n                                    } catch (e) {\n                                    // Ignore JSON parse errors for individual chunks\n                                    }\n                                }\n                            }\n                        } catch (e) {\n                            // Ignore parsing errors, just track timing\n                            if (!firstTokenSent) {\n                                const firstTokenTime = Date.now() - streamStartTime;\n                                console.log(\"\\uD83D\\uDE80 \".concat(provider.toUpperCase(), \" FIRST TOKEN: \").concat(firstTokenTime, \"ms (\").concat(model, \") [fallback detection]\"));\n                                firstTokenSent = true;\n                            }\n                        }\n                    }\n                    // Forward the chunk unchanged\n                    controller.enqueue(value);\n                }\n            } catch (error) {\n                console.error(\"[\".concat(provider, \" Stream Tracking] Error:\"), error);\n                // Phase 1 Optimization: Graceful error handling for connection resets\n                if (error instanceof Error && (error.message.includes('aborted') || error.message.includes('ECONNRESET'))) {\n                    console.log(\"[\".concat(provider, \" Stream] Connection reset detected - closing stream gracefully\"));\n                    controller.close();\n                } else {\n                    controller.error(error);\n                }\n            }\n        }\n    });\n}\n// Enhanced logging for streaming performance\nfunction logStreamingPerformance(provider, model, metrics) {\n    console.log(\"\\uD83D\\uDCCA STREAMING PERFORMANCE: \".concat(provider, \"/\").concat(model));\n    if (metrics.timeToFirstToken !== undefined) {\n        console.log(\"   ⏱️ Time to First Token: \".concat(metrics.timeToFirstToken.toFixed(1), \"ms\"));\n        // Performance categories\n        if (metrics.timeToFirstToken < 500) {\n            console.log(\"   ⚡ EXCELLENT first token performance\");\n        } else if (metrics.timeToFirstToken < 1000) {\n            console.log(\"   ✅ GOOD first token performance\");\n        } else if (metrics.timeToFirstToken < 2000) {\n            console.log(\"   ⚠️ SLOW first token performance\");\n        } else {\n            console.log(\"   \\uD83D\\uDC0C VERY SLOW first token performance\");\n        }\n    }\n    if (metrics.totalStreamTime !== undefined) {\n        console.log(\"   \\uD83D\\uDD04 Total Stream Time: \".concat(metrics.totalStreamTime.toFixed(1), \"ms\"));\n    }\n    if (metrics.totalTokens !== undefined) {\n        console.log(\"   \\uD83C\\uDFAF Total Tokens: \".concat(metrics.totalTokens));\n    }\n    if (metrics.averageTokenLatency !== undefined) {\n        console.log(\"   \\uD83D\\uDCC8 Avg Token Latency: \".concat(metrics.averageTokenLatency.toFixed(1), \"ms/token\"));\n    }\n}\n// Utility to extract provider and model from request context\nfunction getProviderModelFromContext(providerName, modelId) {\n    return {\n        provider: providerName || 'unknown',\n        model: modelId || 'unknown'\n    };\n}\n// Simple token counter for rough estimation\nfunction estimateTokenCount(text) {\n    // Rough estimation: 1 token ≈ 4 characters for English text\n    // This is a simplified approach, real tokenization would be more accurate\n    return Math.ceil(text.length / 4);\n}\n// Performance thresholds for different providers\nconst PERFORMANCE_THRESHOLDS = {\n    EXCELLENT_FIRST_TOKEN: 500,\n    GOOD_FIRST_TOKEN: 1000,\n    SLOW_FIRST_TOKEN: 2000,\n    // Anything above 2000ms is considered very slow\n    EXCELLENT_TOTAL: 3000,\n    GOOD_TOTAL: 5000,\n    SLOW_TOTAL: 10000,\n    TARGET_TOKEN_LATENCY: 50\n};\n// Check if performance meets targets\nfunction evaluatePerformance(metrics) {\n    const firstTokenGrade = !metrics.timeToFirstToken ? 'very_slow' : metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.EXCELLENT_FIRST_TOKEN ? 'excellent' : metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.GOOD_FIRST_TOKEN ? 'good' : metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.SLOW_FIRST_TOKEN ? 'slow' : 'very_slow';\n    const totalTimeGrade = !metrics.totalStreamTime ? 'very_slow' : metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.EXCELLENT_TOTAL ? 'excellent' : metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.GOOD_TOTAL ? 'good' : metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.SLOW_TOTAL ? 'slow' : 'very_slow';\n    const tokenLatencyGrade = !metrics.averageTokenLatency ? 'very_slow' : metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY ? 'excellent' : metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY * 2 ? 'good' : metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY * 4 ? 'slow' : 'very_slow';\n    // Overall grade is the worst of the three\n    const grades = [\n        firstTokenGrade,\n        totalTimeGrade,\n        tokenLatencyGrade\n    ];\n    const gradeOrder = [\n        'excellent',\n        'good',\n        'slow',\n        'very_slow'\n    ];\n    const overallGrade = grades.reduce((worst, current)=>{\n        return gradeOrder.indexOf(current) > gradeOrder.indexOf(worst) ? current : worst;\n    }, 'excellent');\n    return {\n        firstTokenGrade,\n        totalTimeGrade,\n        tokenLatencyGrade,\n        overallGrade\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/streamingUtils.ts\n"));

/***/ })

});